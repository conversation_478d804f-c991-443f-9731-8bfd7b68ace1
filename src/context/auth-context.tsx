"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { User, AuthContextType } from './auth-types';

// Create the AuthContext
const AuthContext = createContext<AuthContextType | undefined>(undefined);
export { AuthContext };

// Create the AuthProvider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  // Fetch the current session on mount
  useEffect(() => {
    const getSession = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/auth/session');
        const data = await response.json();

        if (data.authenticated && data.user) {
          setUser(data.user);
        } else {
          setUser(null);
        }
      } catch (error) {
        console.error('Error getting session:', error);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    getSession();
  }, []);

  // Sign in function
  const signIn = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/auth/signin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 401) {
          toast.error('Invalid email or password. Please try again.');
        } else if (response.status === 403 && data.requiresVerification) {
          // Email not verified, redirect to verification page
          toast.info('Please verify your email to continue.');
          router.push(`/verify?email=${encodeURIComponent(email)}`);
        } else if (response.status === 429) {
          toast.error('Too many login attempts. Please try again in a few minutes.');
        } else {
          toast.error(data.error || 'An error occurred during sign in.');
        }
        return;
      }

      // Success
      setUser(data.user);
      toast.success('Signed in successfully!');
      router.push('/dashboard');
    } catch (error: any) {
      console.error('Sign in error:', error);
      toast.error('An error occurred during sign in. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Sign up function
  const signUp = async (email: string, password: string, firstName: string, lastName: string, autoVerify = false, trusteeInvitationId?: string, selectedPlan: 'free' | 'premium' = 'free') => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          firstName,
          lastName,
          autoVerify,
          trusteeInvitationId,
          selectedPlan
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 409) {
          toast.error('This email is already registered. Please sign in instead.');
        } else if (response.status === 429) {
          toast.error('Too many sign-up attempts. Please wait a few minutes before trying again.');
        } else {
          toast.error(data.error || 'An error occurred during sign up.');
        }
        return;
      }

      // If auto-verify is true, the user is already verified and logged in
      if (autoVerify && data.user) {
        setUser(data.user);
        toast.success('Account created successfully!');
        router.push('/dashboard');
        return;
      }

      // If this was a trustee invitation acceptance, redirect to dashboard
      if (data.isTrusteeAccepted && data.user) {
        setUser(data.user);
        toast.success('Account created and trustee invitation accepted successfully!');
        router.push('/dashboard');
        return;
      }

      // Success - redirect to verification page
      toast.success('Account created! Please enter the verification code sent to your email.');
      const verifyUrl = selectedPlan === 'premium'
        ? `/verify?email=${encodeURIComponent(email)}&plan=premium`
        : `/verify?email=${encodeURIComponent(email)}`;
      router.push(verifyUrl);
    } catch (error: any) {
      console.error('Sign up error:', error);
      toast.error('An error occurred during sign up. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Sign out function
  const signOut = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/auth/signout', {
        method: 'POST',
      });

      if (!response.ok) {
        const data = await response.json();
        toast.error(data.error || 'An error occurred during sign out.');
        return;
      }

      // Success
      setUser(null);
      toast.info('Signed out successfully.');
      router.push('/');
    } catch (error: any) {
      console.error('Sign out error:', error);
      toast.error('An error occurred during sign out. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Verify OTP function
  const verifyOtp = async (email: string, token: string, skipRedirect = false) => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/auth/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, code: token }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 400) {
          toast.error('Invalid or expired verification code. Please try again.');
        } else {
          toast.error(data.error || 'An error occurred during verification.');
        }
        return;
      }

      // Success
      setUser(data.user);
      toast.success('Email verified successfully!');
      if (!skipRedirect) {
        router.push('/dashboard');
      }
    } catch (error: any) {
      console.error('Verification error:', error);
      toast.error('An error occurred during verification. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Resend verification code function
  const resendVerificationCode = async (email: string) => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 429) {
          toast.error('Too many attempts. Please wait a few minutes before trying again.');
        } else {
          toast.error(data.error || 'Failed to resend verification code.');
        }
        return;
      }

      // Success
      toast.success('Verification code sent! Please check your email.');
    } catch (error: any) {
      console.error('Resend verification error:', error);
      toast.error('An error occurred while resending the verification code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Update profile function
  const updateProfile = async (firstName: string, lastName: string) => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/auth/update-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ firstName, lastName }),
      });

      const data = await response.json();

      if (!response.ok) {
        toast.error(data.error || 'An error occurred while updating your profile.');
        return;
      }

      // Success
      setUser(prev => prev ? { ...prev, firstName, lastName } : null);
      toast.success('Profile updated successfully!');
    } catch (error: any) {
      console.error('Update profile error:', error);
      toast.error('An error occurred while updating your profile. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Update email function
  const updateEmail = async (email: string) => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/auth/update-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 409) {
          toast.error('This email is already in use by another account.');
        } else {
          toast.error(data.error || 'An error occurred while updating your email.');
        }
        return;
      }

      // Success - redirect to verification page
      toast.success('Verification code sent! Please verify your new email.');
      router.push(`/verify?email=${encodeURIComponent(email)}`);
    } catch (error: any) {
      console.error('Update email error:', error);
      toast.error('An error occurred while updating your email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Update password function
  const updatePassword = async (password: string) => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/auth/update-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      });

      const data = await response.json();

      if (!response.ok) {
        toast.error(data.error || 'An error occurred while updating your password.');
        return;
      }

      // Success
      toast.success('Password updated successfully!');
    } catch (error: any) {
      console.error('Update password error:', error);
      toast.error('An error occurred while updating your password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const contextValue: AuthContextType = {
    session: user ? { 
      user,
      access_token: '', // Will be populated from API response
      refresh_token: '', // Will be populated from API response
      expires_in: 3600, // Default value
      token_type: 'bearer' // Default value
    } : null,
    user,
    loading: isLoading,
    signIn,
    signUp,
    signOut,
    verifyOtp,
    updateProfile,
    updateEmail,
    updatePassword,
    resendVerificationCode,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Create a hook to use the AuthContext
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
