"use client";

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './auth-context';
import { toast } from 'sonner';
import type { 
  SubscriptionPlan, 
  SubscriptionDetails, 
  SubscriptionContextType 
} from './subscription-types';

export const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export const SubscriptionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [plan, setPlan] = useState<SubscriptionPlan>('free');
  const [isLoading, setIsLoading] = useState(true);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [subscriptionDetails, setSubscriptionDetails] = useState<SubscriptionDetails | null>(null);

  const checkSubscription = useCallback(async () => {
    if (!user) {
      setIsLoading(false);
      setPlan('free');
      setIsSubscribed(false);
      setSubscriptionDetails(null);
      return;
    }

    try {
      setIsLoading(true);

      const response = await fetch('/api/stripe/subscription');

      if (!response.ok) {
        throw new Error('Failed to fetch subscription status');
      }

      const data = await response.json();

      setPlan(data.plan);
      setIsSubscribed(data.isSubscribed);
      setSubscriptionDetails(data.subscription);
    } catch (error: any) {
      console.error('Error checking subscription:', error);
      setPlan('free');
      setIsSubscribed(false);
      setSubscriptionDetails(null);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  const createCheckout = async (selectedPlan: 'premium'): Promise<string | null> => {
    if (!user) {
      toast.error('You must be logged in to subscribe');
      return null;
    }

    try {
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ plan: selectedPlan }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create checkout session');
      }

      const data = await response.json();
      return data.url;
    } catch (error: any) {
      console.error('Error creating checkout session:', error);
      toast.error(error.message || 'Failed to create checkout session');
      return null;
    }
  };

  useEffect(() => {
    if (user) {
      checkSubscription();
    } else {
      setPlan('free');
      setIsSubscribed(false);
      setSubscriptionDetails(null);
    }
  }, [user, checkSubscription]);

  return (
    <SubscriptionContext.Provider
      value={{
        plan,
        isLoading,
        isSubscribed,
        subscriptionDetails,
        checkSubscription,
        createCheckout,
      }}
    >
      {children}
    </SubscriptionContext.Provider>
  );
};
