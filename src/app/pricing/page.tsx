"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/auth-context';
import { useSubscription } from '@/context/SubscriptionContext';
import { Check, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
// Remove client-side Stripe imports - these should only be used server-side
import { toast } from 'sonner';

export default function PricingPage() {
  const router = useRouter();
  const { user } = useAuth();
  const { plan: currentPlan, isSubscribed, createCheckout } = useSubscription();
  const [isLoading, setIsLoading] = useState(false);

  // Check for checkout parameter to auto-trigger premium checkout
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const shouldCheckout = urlParams.get('checkout') === 'true';

    if (shouldCheckout && user && currentPlan === 'free') {
      // Auto-trigger premium checkout
      const triggerCheckout = async () => {
        try {
          const checkoutUrl = await createCheckout('premium');
          if (checkoutUrl) {
            window.location.href = checkoutUrl;
          }
        } catch (error) {
          console.error('Error creating checkout session:', error);
          toast.error('Failed to create checkout session');
        }
      };
      triggerCheckout();
    }
  }, [user, currentPlan, createCheckout]);

  // Define plan details directly in the component
  const freePlan = {
    name: 'Essential Legacy',
    price: 0,
    features: [
      'Unlimited asset inventory',
      'Single document storage',
      'Up to 1 trustee',
      'Basic will advice',
      'Unlimited contacts & wishes',
      'Unlimited service sunset',
    ],
  };

  const premiumPlan = {
    name: 'Legacy Preserver',
    price: 2999, // $29.99
    features: [
      'Unlimited asset inventory',
      '5GB vault storage for documents',
      '100 time capsules (up to 10GB)',
      'Up to 5 trustees',
      'Unlimited contacts & wishes',
      'Unlimited service sunset',
      'Priority customer support',
      'Unlimited will advice',
    ],
  };

  const handleSubscribe = async (planId: 'premium') => {
    if (!user) {
      toast.info('Please sign in to subscribe');
      router.push('/login?redirect=/pricing');
      return;
    }

    setIsLoading(true);
    try {
      const checkoutUrl = await createCheckout('premium');
      if (checkoutUrl) {
        window.location.href = checkoutUrl;
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      toast.error('Failed to create checkout session');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-16 px-4">
      <div className="text-center mb-16">
        <h1 className="text-4xl font-bold mb-4">Simple, Transparent Pricing</h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Choose the plan that works best for you and your family's legacy planning needs.
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
        {/* Free Plan */}
        <Card className={`relative overflow-hidden border-2 ${currentPlan === 'free' ? 'border-green-500' : 'border-gray-200'} transition-all duration-300 hover:shadow-md`}>
          {currentPlan === 'free' && (
            <div className="absolute top-4 right-4 bg-green-500 text-white px-3 py-1 text-sm font-medium rounded-md">
              Your Plan
            </div>
          )}
          <CardHeader className="pb-8">
            <CardTitle className="text-2xl">Essential Legacy</CardTitle>
            <CardDescription>Basic features for everyone</CardDescription>
            <div className="mt-4">
              <span className="text-4xl font-bold">$0</span>
              <span className="text-gray-500 ml-2">/forever</span>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <ul className="space-y-3">
              {freePlan.features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => router.push('/register')}
              disabled={!!user}
            >
              {user ? 'Current Plan' : 'Get Started'}
            </Button>
          </CardFooter>
        </Card>

        {/* Premium Plan */}
        <Card className={`relative overflow-hidden border-2 ${currentPlan === 'premium' ? 'border-green-500' : 'border-primary'} transition-all duration-300 hover:shadow-md`}>
          {currentPlan === 'premium' ? (
            <div className="absolute top-4 right-4 bg-green-500 text-white px-3 py-1 text-sm font-medium rounded-md">
              Your Plan
            </div>
          ) : (
            <div className="absolute top-4 right-4 bg-primary text-white px-3 py-1 text-sm font-medium rounded-md">
              Recommended
            </div>
          )}
          <CardHeader className="pb-8">
            <CardTitle className="text-2xl">Legacy Preserver</CardTitle>
            <CardDescription>Advanced features for complete peace of mind</CardDescription>
            <div className="mt-4">
              <span className="text-4xl font-bold">$29.99</span>
              <span className="text-gray-500 ml-2">/ year</span>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <ul className="space-y-3">
              {premiumPlan.features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Button
              className="w-full"
              onClick={() => handleSubscribe('premium')}
              disabled={isLoading || currentPlan === 'premium'}
            >
              {isLoading ? (
                <span className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </span>
              ) : currentPlan === 'premium' ? (
                'Current Plan'
              ) : (
                'Upgrade to Premium'
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>

      <div className="text-center mt-16 text-gray-600">
        <p className="mb-2">All plans include:</p>
        <ul className="space-y-1">
          <li>Secure data storage</li>
          <li>Regular updates and improvements</li>
          <li>Email support</li>
        </ul>
      </div>
    </div>
  );
}
